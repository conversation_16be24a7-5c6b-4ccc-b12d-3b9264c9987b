{"name": "Lark Samples Dev Environment", "image": "mcr.microsoft.com/devcontainers/base:ubuntu", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "golang.go", "redhat.java", "vscjava.vscode-java-pack", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-tslint-plugin", "streetsidesoftware.code-spell-checker"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "java.jdt.ls.java.home": "/usr/local/openjdk-17"}}}, "features": {"ghcr.io/devcontainers/features/node:1": {"version": "22"}, "ghcr.io/devcontainers/features/python:1": {"version": "3.11"}, "ghcr.io/devcontainers/features/go:1": {"version": "1.24"}, "ghcr.io/devcontainers/features/java:1": {"version": "17", "installMaven": true}}}