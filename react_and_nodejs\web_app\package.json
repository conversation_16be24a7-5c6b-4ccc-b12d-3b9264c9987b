{"name": "init", "version": "0.1.0", "private": true, "dependencies": {"@larksuiteoapi/node-sdk": "^1.0.6", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "express": "^4.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "vconsole": "^3.14.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "concurrently \"npm run dev\" \"npm run server\"", "server": "node --inspect ./src/server/index.js", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/express": "^4.17.13", "concurrently": "^7.3.0"}, "proxy": "http://localhost:3001"}