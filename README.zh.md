# 飞书示例代码

## 概要
本仓库用于开放飞书示例代码，帮助开发者快捷使用飞书能力，项目包含以下示例代码：
- [快速开发机器人](robot_quick_start/python/README.zh.md)
- [快速开发网页应用](web_app_with_jssdk/python/README.zh.md)
- [快速实现网页应用免登](web_app_with_auth/python/README.zh.md)
- [react+nodejs快速实现网页应用和网页免登应用](react_and_nodejs/web_app/README.zh.md)
- [react+nodejs快速开发机器人](react_and_nodejs/robot/README.zh.md)
- 回声机器人 [go](echo_robot/go/README.md) [python](echo_robot/python/README.md) [java](echo_robot/java/README.md) [nodejs](echo_robot/nodejs/README.md)
- 审批机器人 [go](interactive_bot/go/README.md) [python](interactive_bot/python/README.md) [java](interactive_bot/java/README.md) [nodejs](interactive_bot/nodejs/README.md)

## License
- Apache 2.0